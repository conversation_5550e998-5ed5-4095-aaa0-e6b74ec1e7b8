{"name": "grok2api", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"start": "node index.js"}, "author": "yxmiler", "dependencies": {"express": "^4.18.2", "node-fetch": "^3.3.2", "dotenv": "^16.3.1", "cors": "^2.8.5", "form-data": "^4.0.0", "puppeteer": "^22.8.2", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "moment": "^2.30.1", "chalk": "^5.4.1", "uuid": "^9.0.0"}}