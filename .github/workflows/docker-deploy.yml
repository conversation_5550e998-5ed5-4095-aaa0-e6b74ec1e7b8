name: Docker Multiarch CI

on:
  push:
    branches: [ "main" ]
    paths-ignore: 
      - 'README.md' # 忽略README.md修改触发
      - '.dockerignore' # 忽略.dockerignore修改触发
  pull_request:
    branches: [ "main" ]
    paths-ignore:
      - 'README.md' # 忽略README.md修改触发
      - '.dockerignore' # 忽略.dockerignore修改触发

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    
    steps:
    - name: Checkout code
      uses: actions/checkout@v3
    
    - name: Set up QEMU
      uses: docker/setup-qemu-action@v2
    
    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v2
    
    - name: Login to Docker Hub
      uses: docker/login-action@v2
      with:
        username: ${{ secrets.DOCKERHUB_USERNAME }}
        password: ${{ secrets.DOCKERHUB_TOKEN }}
    
    - name: Build and push
      uses: docker/build-push-action@v3
      with:
        context: .
        platforms: linux/amd64,linux/arm64
        push: true
        tags: |
          ${{ secrets.DOCKERHUB_USERNAME }}/grok2api_python:latest
          ${{ secrets.DOCKERHUB_USERNAME }}/grok2api_python:${{ github.sha }}
        cache-from: type=gha
        cache-to: type=gha,mode=max
