version: '3.8'
services:
  grok2api_python:
    image: yxmiler/grok2api_python:latest
    container_name: grok2api_python
    ports:
      - "8081:3000"
    environment:
      - API_KEY=your_api_key
      - TUMY_KEY=你的图床key,和PICGO_KEY 二选一
      - PICGO_KEY=你的图床key,和TUMY_KEY二选一
      - IS_TEMP_CONVERSATION=true
      - IS_CUSTOM_SSO=false
      - ISSHOW_SEARCH_RESULTS=false
      - PORT=3000
      - SHOW_THINKING=true
      - SSO=your_sso
    restart: unless-stopped
