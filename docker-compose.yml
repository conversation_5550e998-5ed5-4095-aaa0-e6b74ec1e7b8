services:
  grok2api_python:
    image: yxmiler/grok2api_python:latest
    container_name: grok2api_python
    ports:
      - "${HOST_PORT:-50009}:${CONTAINER_PORT:-8081}"
    environment:
      - API_KEY=${API_KEY}
      - TUMY_KEY=${TUMY_KEY}
      - PICGO_KEY=${PICGO_KEY}
      - IS_TEMP_CONVERSATION=${IS_TEMP_CONVERSATION}
      - IS_CUSTOM_SSO=${IS_CUSTOM_SSO}
      - ISSHOW_SEARCH_RESULTS=${ISSHOW_SEARCH_RESULTS}
      - PORT=${PORT}
      - SHOW_THINKING=${SHOW_THINKING}
      - SSO=${SSO}
    restart: unless-stopped
