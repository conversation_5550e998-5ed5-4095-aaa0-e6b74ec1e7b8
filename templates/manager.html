<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>GrokAPI管理面板</title>
    <style type="text/css">@font-face {font-family:Inter;font-style:normal;font-weight:300;src:url(/cf-fonts/v/inter/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:300;src:url(/cf-fonts/v/inter/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:300;src:url(/cf-fonts/v/inter/5.0.16/greek-ext/wght/normal.woff2);unicode-range:U+1F00-1FFF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:300;src:url(/cf-fonts/v/inter/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:300;src:url(/cf-fonts/v/inter/5.0.16/greek/wght/normal.woff2);unicode-range:U+0370-03FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:300;src:url(/cf-fonts/v/inter/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:300;src:url(/cf-fonts/v/inter/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/greek-ext/wght/normal.woff2);unicode-range:U+1F00-1FFF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/greek/wght/normal.woff2);unicode-range:U+0370-03FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:400;src:url(/cf-fonts/v/inter/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/greek-ext/wght/normal.woff2);unicode-range:U+1F00-1FFF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/greek/wght/normal.woff2);unicode-range:U+0370-03FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:600;src:url(/cf-fonts/v/inter/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/cyrillic-ext/wght/normal.woff2);unicode-range:U+0460-052F,U+1C80-1C88,U+20B4,U+2DE0-2DFF,U+A640-A69F,U+FE2E-FE2F;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/greek-ext/wght/normal.woff2);unicode-range:U+1F00-1FFF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/latin/wght/normal.woff2);unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+0304,U+0308,U+0329,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/latin-ext/wght/normal.woff2);unicode-range:U+0100-02AF,U+0304,U+0308,U+0329,U+1E00-1E9F,U+1EF2-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/vietnamese/wght/normal.woff2);unicode-range:U+0102-0103,U+0110-0111,U+0128-0129,U+0168-0169,U+01A0-01A1,U+01AF-01B0,U+0300-0301,U+0303-0304,U+0308-0309,U+0323,U+0329,U+1EA0-1EF9,U+20AB;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/cyrillic/wght/normal.woff2);unicode-range:U+0301,U+0400-045F,U+0490-0491,U+04B0-04B1,U+2116;font-display:swap;}@font-face {font-family:Inter;font-style:normal;font-weight:700;src:url(/cf-fonts/v/inter/5.0.16/greek/wght/normal.woff2);unicode-range:U+0370-03FF;font-display:swap;}</style>
    <style>
        :root {
            --primary: #3498db;
            --primary-hover: #1575b5;
            --secondary: #64748B;
            --success: #10B981;
            --danger: #EF4444;
            --warning: #F59E0B;
            --bg-light: #F8FAFC;
            --bg-white: #FFFFFF;
            --text-dark: #1F2937;
            --text-muted: #64748B;
            --border: #E2E8F0;
            --shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
            --card-border: #E2E8F0;
            --progress-bg: #E2E8F0;
            --status-active-bg: rgba(16, 185, 129, 0.1);
            --status-expired-bg: rgba(239, 68, 68, 0.1);
            --progress-fill-success: #10B981;
            --progress-fill-warning: #F59E0B;
            --progress-fill-danger: #EF4444;
        }

        * { box-sizing: border-box; margin: 0; padding: 0; }
        body { font-family: 'Inter', sans-serif; background: var(--bg-light); color: var(--text-dark); line-height: 1.6; min-height: 100vh; padding-top: 5rem; }
        .container { max-width: 1280px; margin: 0 auto; padding: 0 1.5rem; }
        .card { background: var(--bg-white); border-radius: 0.75rem; box-shadow: var(--shadow); padding: 1.5rem; margin-bottom: 1.5rem; border: 1px solid var(--card-border); }
        
        .search-section { 
            position: fixed; 
            top: 0; 
            left: 0; 
            right: 0; 
            z-index: 100; 
            background: var(--bg-white); 
            box-shadow: var(--shadow); 
            padding: 1rem 1.5rem; 
            margin: 0; 
        }
        .search-section .card { 
            padding: 0; 
            margin: 0; 
            box-shadow: none; 
            border: none; 
        }
        .search-input { 
            width: 100%; 
            padding: 0.75rem 1rem 0.75rem 2.5rem; 
            border: 1px solid #CBD5E1; 
            border-radius: 0.5rem; 
            font-size: 0.9rem; 
            background: #F9FAFB url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none" stroke="%2364748B" stroke-width="2"><circle cx="7" cy="7" r="5"/><path d="M11 11l4 4"/></svg>') no-repeat 0.75rem center; 
            transition: border-color 0.2s ease, box-shadow 0.2s ease; 
        }
        .search-input:focus { 
            border-color: #0f5fc3; 
            outline: none; 
            box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2); 
        }
        .btn-base { padding: 0.5rem 1rem; border-radius: 0.5rem; border: none; cursor: pointer; font-weight: 500; display: inline-flex; align-items: center; gap: 0.5rem; transition: transform 0.2s ease, background 0.3s ease, box-shadow 0.2s ease; }
        .btn { background: linear-gradient(135deg, var(--primary) 0%, #0f5fc3 100%); color: white; }
        .btn:hover { background: linear-gradient(135deg, var(--primary-hover) 0%, #0f5fc3 100%); transform: translateY(-2px); box-shadow: 0 4px 15px rgba(107, 70, 193, 0.3); }
        .btn-secondary { background: linear-gradient(135deg, var(--primary) 0%, #0f5fc3 100%); color: white; width: 2rem; height: 2rem; padding: 0; justify-content: center; }
        .btn-secondary:hover { background: linear-gradient(135deg, var(--primary-hover) 0%, #0f5fc3 100%); transform: translateY(-1px); box-shadow: 0 2px 8px rgba(107, 70, 193, 0.3); }
        .btn-danger { background: var(--danger); width: 2rem; height: 2rem; padding: 0; justify-content: center; }
        .btn-danger:hover { background: #DC2626; }
        .token-manage-grid { display: grid; grid-template-columns: 1fr; gap: 1.5rem; margin-top: 1rem; }
        .input-group { display: flex; gap: 0.75rem; flex-wrap: wrap; align-items: center; }
        .input-field { flex: 1; min-width: 0; padding: 0.75rem; border: 1px solid #CBD5E1; border-radius: 0.5rem; font-size: 0.9rem; background: #F9FAFB; transition: border-color 0.2s ease, box-shadow 0.2s ease; }
        .input-field:focus { border-color: #0f5fc3; outline: none; box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2); }
        #statusFilter, #modelSelect { background: #F9FAFB; border: 1px solid #CBD5E1; color: var(--text-dark); }
        #statusFilter:focus, #modelSelect:focus { border-color: #0f5fc3; box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.2); }
        .token-grid { display: grid; grid-template-columns: 1fr; gap: 1.5rem; }
        .token-card { background: var(--bg-white); border: 1px solid var(--card-border); border-radius: 0.75rem; box-shadow: var(--shadow); padding: 1rem; display: flex; flex-direction: column; gap: 1rem; transition: transform 0.2s ease; max-width: 100%; overflow: hidden; }
        .token-card:hover { transform: translateY(-4px); }
        .token-checkbox { display: none; }
        .token-checkbox.show { display: block; position: absolute; top: 1rem; left: 1rem; }
        .token-header { display: flex; flex-wrap: wrap; justify-content: space-between; align-items: center; gap: 0.5rem; border-bottom: 1px solid var(--border); padding-bottom: 0.75rem; }
        .token-title { font-weight: 600; font-size: 0.9rem; color: var(--text-dark); flex: 1 1 auto; max-width: calc(100% - 80px); overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .token-actions { display: flex; gap: 0.5rem; flex-shrink: 0; }
        .model-list { display: grid; gap: 0.75rem; }
        .model-item { display: grid; grid-template-columns: 1fr 2fr 80px; gap: 0.5rem; align-items: center; padding: 0.5rem; background: rgba(248, 250, 252, 0.5); border-radius: 0.5rem; }
        .model-name { font-size: 0.85rem; font-weight: 500; color: var(--text-dark); white-space: nowrap; overflow: hidden; text-overflow: ellipsis;}     
        .progress-container { display: flex; align-items: center; gap: 0.5rem; }
        .progress-bar { flex: 1; height: 0.375rem; background: var(--progress-bg); border-radius: 1rem; overflow: hidden; }
        .progress-fill { height: 100%; border-radius: 1rem; transition: width 0.3s ease; }
        .progress-text { font-size: 0.75rem; color: var(--text-muted); min-width: 40px; }
        .status { font-size: 0.75rem; padding: 0.25rem 0.75rem; border-radius: 1rem; text-align: center; font-weight: 500; }
        .status-active { background: var(--status-active-bg); color: var(--success); }
        .status-expired { background: var(--status-expired-bg); color: var(--danger); position: relative; }
        .status-expired .tooltip { position: absolute; bottom: 100%; left: 50%; transform: translateX(-50%); background: var(--text-dark); color: white; padding: 0.25rem 0.5rem; border-radius: 0.25rem; font-size: 0.75rem; white-space: nowrap; opacity: 0; visibility: hidden; transition: opacity 0.2s; margin-bottom: 0.5rem; }
        .status-expired:hover .tooltip { opacity: 1; visibility: visible; }
        .notification { position: fixed; top: 5rem; left: 50%; transform: translateX(-50%); background: var(--primary); color: white; padding: 0.75rem 1.5rem; border-radius: 0.5rem; box-shadow: var(--shadow); z-index: 1000; display: none; animation: slideIn 0.3s ease; }
        @keyframes slideIn { from { transform: translateX(-50%) translateY(-100%); opacity: 0; } to { transform: translateX(-50%) translateY(0); opacity: 1; } }

        .overview-section { background: var(--bg-white); border-radius: 1rem; padding: 1.5rem; box-shadow: 0 6px 20px rgba(0, 0, 0, 0.06); position: relative; overflow: hidden; }
        .overview-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem; }
        .overview-title { font-size: 1.5rem; font-weight: 700; color: var(--text-dark); }
        .overview-actions { display: flex; gap: 0.75rem; }
        .overview-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(180px, 1fr)); gap: 1rem; }
        .overview-card { background: linear-gradient(135deg, #F9FAFB 0%, #F1F5F9 100%); border-radius: 0.75rem; padding: 1rem; box-shadow: 0 4px 12px rgba(0, 0, 0, 0.04); transition: transform 0.2s ease, box-shadow 0.2s ease; display: flex; align-items: center; gap: 1rem; position: relative; border: 1px solid rgba(107, 70, 193, 0.1); }
        .overview-card:hover { transform: translateY(-4px); box-shadow: 0 8px 20px rgba(107, 70, 193, 0.15); }
        .overview-icon { width: 2.5rem; height: 2.5rem; background: linear-gradient(135deg, rgba(107, 70, 193, 0.1) 0%, rgba(139, 92, 246, 0.1) 100%); border-radius: 0.5rem; display: flex; align-items: center; justify-content: center; color: var(--primary); flex-shrink: 0; }
        .overview-content { display: flex; flex-direction: column; gap: 0.25rem; }
        .overview-label { font-size: 0.9rem; font-weight: 500; color: var(--text-muted); text-transform: uppercase; letter-spacing: 0.05px; }
        .overview-value { font-size: 1.75rem; font-weight: 700; color: var(--primary); }
        .overview-card::after { content: attr(data-tooltip); position: absolute; top: -2.5rem; left: 50%; transform: translateX(-50%); background: rgba(31, 41, 55, 0.9); color: white; padding: 0.5rem 1rem; border-radius: 0.5rem; font-size: 0.85rem; white-space: nowrap; opacity: 0; visibility: hidden; transition: opacity 0.2s ease; pointer-events: none; }
        .overview-card:hover::after { opacity: 1; visibility: visible; }

        .pagination { display: flex; justify-content: center; align-items: center; gap: 0.5rem; margin-top: 1.5rem; }
        .pagination-btn { padding: 0.5rem 1rem; border-radius: 0.5rem; border: 1px solid var(--border); background: var(--bg-white); cursor: pointer; font-size: 0.9rem; transition: background 0.2s ease; }
        .pagination-btn:hover { background: var(--bg-light); }
        .pagination-btn:disabled { opacity: 0.5; cursor: not-allowed; }
        .pagination-select { padding: 0.5rem; border: 1px solid var(--border); border-radius: 0.5rem; font-size: 0.9rem; }

        @media (max-width: 768px) {
            .overview-grid { grid-template-columns: repeat(auto-fit, minmax(150px, 1fr)); gap: 0.75rem; }
            .overview-card { padding: 0.75rem; }
            .overview-icon { width: 2rem; height: 2rem; }
            .overview-value { font-size: 1.5rem; }
            .overview-label { font-size: 0.85rem; }
            .token-grid { grid-template-columns: 1fr; gap: 1rem; }
            .token-card { padding: 0.75rem; }
        }

        @media (min-width: 768px) {
            .token-grid { grid-template-columns: repeat(auto-fill, minmax(360px, 1fr)); }
            .token-card { min-height: 240px; }
            .token-manage-grid { grid-template-columns: repeat(2, 1fr); }
        }

        @media (max-width: 480px) {
            body { padding: 5rem 1rem 1rem; }
            .card { padding: 1rem; }
            .overview-section { padding: 1rem; }
            .overview-header { flex-direction: column; align-items: flex-start; gap: 1rem; }
            .overview-actions { width: 100%; justify-content: flex-end; }
            .overview-grid { grid-template-columns: 1fr; gap: 0.75rem; }
            .overview-card { padding: 1rem; }
            .overview-value { font-size: 1.5rem; }
            .search-section { padding: 0.5rem 1rem; }
            .search-section .input-group { flex-direction: column; gap: 0.5rem; }
            .search-input, #statusFilter { width: 100%; }
            .pagination { flex-wrap: wrap; gap: 0.75rem; }
            .input-group label { width: 100%; margin-bottom: 0.5rem; }
            .input-field { width: 100%; }
        }
    </style>
</head>
<body>
    <div class="search-section">
        <div class="card">
            <div class="input-group">
                <input type="text" class="search-input" id="searchInput" placeholder="搜索 Token..." aria-label="搜索 Token" style="flex: 1;">
                <select class="input-field" id="statusFilter" style="width: 120px;" aria-label="筛选 Token 状态">
                    <option value="all">全部</option>
                    <option value="active">活跃</option>
                    <option value="expired">失效</option>
                </select>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="overview-section card">
            <div class="overview-header">
                <h2 class="overview-title">概览</h2>
                <div class="overview-actions">
                    <button class="btn btn-base" id="batchDeleteTokens" aria-label="批量删除选中的 Token">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 6h18M9 6V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v2M10 11v6M14 11v6M4 6v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V6"/>
                        </svg>
                        批量删除
                    </button>
                    <button class="btn btn-base" id="refreshTokens" aria-label="刷新 Token 列表">
                        <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8"/>
                            <path d="M21 3v5h-5"/>
                            <path d="M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16"/>
                            <path d="M3 21v-5h5"/>
                        </svg>
                        刷新
                    </button>
                </div>
            </div>
            <div class="overview-grid">
                <div class="overview-card" data-tooltip="总共管理的Token数量">
                    <div class="overview-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"/>
                            <rect x="8" y="2" width="8" height="4" rx="1"/>
                        </svg>
                    </div>
                    <div class="overview-content">
                        <span class="overview-label">Token 总数</span>
                        <span class="overview-value" id="totalTokens">0</span>
                    </div>
                </div>
                <div class="overview-card" data-tooltip="grok-2 模型剩余可用次数">
                    <div class="overview-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2v20M2 12h20"/>
                        </svg>
                    </div>
                    <div class="overview-content">
                        <span class="overview-label">grok-2</span>
                        <span class="overview-value" id="grok-2-count">0</span>
                    </div>
                </div>
                <div class="overview-card" data-tooltip="grok-3 模型剩余可用次数">
                    <div class="overview-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 2v4M12 18v4"/>
                        </svg>
                    </div>
                    <div class="overview-content">
                        <span class="overview-label">grok-3</span>
                        <span class="overview-value" id="grok-3-count">0</span>
                    </div>
                </div>
                <div class="overview-card" data-tooltip="grok-3-deepsearch 模型剩余可用次数">
                    <div class="overview-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="M21 21l-4.35-4.35"/>
                        </svg>
                    </div>
                    <div class="overview-content">
                        <span class="overview-label">grok-3-deepsearch</span>
                        <span class="overview-value" id="grok-3-deepsearch-count">0</span>
                    </div>
                </div>
                <div class="overview-card" data-tooltip="grok-3-deepersearch 模型剩余可用次数">
                    <div class="overview-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="11" cy="11" r="8"/>
                            <path d="M21 21l-4.35-4.35"/>
                        </svg>
                    </div>
                    <div class="overview-content">
                        <span class="overview-label">grok-3-deepersearch</span>
                        <span class="overview-value" id="grok-3-deepersearch-count">0</span>
                    </div>
                </div>                
                <div class="overview-card" data-tooltip="grok-3-reasoning 模型剩余可用次数">
                    <div class="overview-icon">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M12 2a10 10 0 0 0-10 10c0 5 8 13 10 13s10-8 10-13a10 10 0 0 0-10-10z"/>
                        </svg>
                    </div>
                    <div class="overview-content">
                        <span class="overview-label">grok-3-reasoning</span>
                        <span class="overview-value" id="grok-3-reasoning-count">0</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="card">
            <h3>Token 管理</h3>
            <div class="token-manage-grid">
                <div>
                    <h4 style="margin-bottom: 0.75rem;">添加单个 SSO Token</h4>
                    <div class="input-group">
                        <input type="text" class="input-field" id="singleTokenInput" placeholder="输入单个 SSO Token" aria-label="输入单个 SSO Token">
                        <button class="btn btn-base" id="addSingleTokenBtn" aria-label="添加单个 SSO Token">添加</button>
                    </div>
                </div>
                <div>
                    <h4 style="margin-bottom: 0.75rem;">设置 CF Clearance</h4>
                    <div class="input-group">
                        <input type="text" class="input-field" id="cfInput" placeholder="输入 CF Clearance" aria-label="输入 CF Clearance">
                        <button class="btn btn-base" id="setCfBtn" aria-label="设置 CF Clearance">设置</button>
                    </div>
                </div>
                <div>
                    <h4 style="margin-bottom: 0.75rem;">批量添加 SSO Token</h4>
                    <div class="input-group">
                        <input type="text" class="input-field" id="batchTokenInput" placeholder="输入多个 SSO Token（用逗号隔开，如 ey1,ey2）" aria-label="输入多个 SSO Token">
                        <button class="btn btn-base" id="addBatchTokenBtn" aria-label="批量添加 SSO Token">添加</button>
                    </div>
                </div>
                <div>
                    <h4 style="margin-bottom: 0.75rem;">检测模型可用性</h4>
                    <div class="input-group">
                        <select class="input-field" id="modelSelect" style="width: 150px;" aria-label="选择要检测的模型">
                            <option value="grok-2">grok-2</option>
                            <option value="grok-3" selected>grok-3</option>
                            <option value="grok-3-reasoning">grok-3-reasoning</option>
                            <option value="grok-3-deepsearch">grok-3-deepsearch</option>
                        </select>
                        <button class="btn btn-base" id="testAvailabilityBtn" aria-label="检测模型可用性">检测</button>
                    </div>
                </div>
                <div>
                    <h4 style="margin-bottom: 0.75rem;">Base URL</h4>
                    <div class="input-group">
                        <input type="text" class="input-field" id="baseUrl" readonly aria-label="Base URL" value="">
                        <button class="btn btn-base" id="copyBaseUrlBtn" aria-label="复制 Base URL">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2"/>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                            </svg>
                            复制
                        </button>
                    </div>
                </div>
                <div>
                    <h4 style="margin-bottom: 0.75rem;">API Key</h4>
                    <div class="input-group">
                        <input type="text" class="input-field" id="apiKey" aria-label="API Key">
                        <button class="btn btn-base" id="copyApiKeyBtn" aria-label="复制 API Key">
                            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <rect x="9" y="9" width="13" height="13" rx="2"/>
                                <path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/>
                            </svg>
                            复制
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div class="token-grid" id="tokenGrid"></div>
        <div class="pagination" id="pagination">
            <button class="pagination-btn" id="prevPage" aria-label="上一页">上一页</button>
            <select class="pagination-select" id="pageSelect" aria-label="选择页面"></select>
            <button class="pagination-btn" id="nextPage" aria-label="下一页">下一页</button>
        </div>
    </div>

    <div class="notification" id="notification"></div>

    <script>
        const modelConfig = {
            "grok-2": { RequestFrequency: 30, ExpirationTime: 3600000 },
            "grok-3": { RequestFrequency: 20, ExpirationTime: 7200000 },
            "grok-3-deepsearch": { RequestFrequency: 10, ExpirationTime: 86400000 },
            "grok-3-deepersearch": { RequestFrequency: 3, ExpirationTime: 86400000 },
            "grok-3-reasoning": { RequestFrequency: 10, ExpirationTime: 86400000 }
        };

        let tokenMap = {};
        let batchDeleteMode = false;
        let lastUpdateTime = 0;
        let currentPage = 1;
        const itemsPerPage = 30;

        function getProgressColor(percentage, isValid) {
            if (!isValid) return '#94A3B8';
            if (percentage > 70) return 'var(--progress-fill-danger)';
            if (percentage > 30) return 'var(--progress-fill-warning)';
            return 'var(--progress-fill-success)';
        }

        function calculateModelRemaining() {
            const modelRemaining = {};
            Object.keys(modelConfig).forEach(modelName => {
                const maxRequests = modelConfig[modelName].RequestFrequency;
                modelRemaining[modelName] = 0;
                Object.values(tokenMap).forEach(tokenData => {
                    const modelData = tokenData[modelName];
                    if (modelData.isValid) {
                        modelRemaining[modelName] += maxRequests - modelData.totalRequestCount;
                    }
                });
            });
            return modelRemaining;
        }

        function updateTokenCounters() {
            const totalTokensElement = document.getElementById('totalTokens');
            if (totalTokensElement) {
                totalTokensElement.textContent = Object.keys(tokenMap).length;
            } else {
                console.warn('Element with ID "totalTokens" not found.');
            }

            const modelRemaining = calculateModelRemaining();
            const modelIds = ['grok-2', 'grok-3', 'grok-3-deepsearch', 'grok-3-deepersearch', 'grok-3-reasoning'];
            modelIds.forEach(modelName => {
                const countElement = document.getElementById(`${modelName}-count`);
                if (countElement) {
                    countElement.textContent = modelRemaining[modelName] || 0;
                } else {
                    console.warn(`Element with ID "${modelName}-count" not found.`);
                }
            });
        }

        async function updateExpiredTokenTimers() {
            const currentTime = Date.now();
            if (currentTime - lastUpdateTime < 5000) return;

            let shouldRefresh = false;
            Object.values(tokenMap).forEach(tokenData => {
                Object.entries(tokenData).forEach(([modelName, modelData]) => {
                    if (!modelData.isValid) {
                        const recoveryTime = modelData.invalidatedTime + modelConfig[modelName].ExpirationTime;
                        if (recoveryTime <= currentTime) {
                            shouldRefresh = true;
                        }
                    }
                });
            });

            if (shouldRefresh) {
                lastUpdateTime = currentTime;
                await fetchTokenMap();
                showNotification('Token 状态已自动更新');
            } else {
                renderTokenDiff(tokenMap);
            }
        }

        function getTooltipText(invalidatedTime, expirationTime) {
            const currentTime = Date.now();
            const recoveryTime = invalidatedTime + expirationTime;
            const remainingTime = recoveryTime - currentTime;
            if (remainingTime > 0) {
                const minutes = Math.floor(remainingTime / 60000);
                const seconds = Math.floor((remainingTime % 60000) / 1000);
                return `${minutes}分${seconds}秒后恢复`;
            }
            return '已可恢复';
        }

        function createTokenCard(token, tokenData) {
            const tokenCard = document.createElement('div');
            tokenCard.className = 'token-card';
            tokenCard.setAttribute('data-token', token);

            const checkbox = document.createElement('input');
            checkbox.type = 'checkbox';
            checkbox.className = `token-checkbox ${batchDeleteMode ? 'show' : ''}`;
            checkbox.value = token;

            const tokenHeader = document.createElement('div');
            tokenHeader.className = 'token-header';

            const tokenTitle = document.createElement('div');
            tokenTitle.className = 'token-title';
            tokenTitle.textContent = token;
            tokenTitle.title = token;

            const tokenActions = document.createElement('div');
            tokenActions.className = 'token-actions';

            const copyBtn = document.createElement('button');
            copyBtn.className = 'btn btn-secondary btn-base';
            copyBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="9" y="9" width="13" height="13" rx="2"/><path d="M5 15H4a2 2 0 0 1-2-2V4a2 2 0 0 1 2-2h9a2 2 0 0 1 2 2v1"/></svg>';
            copyBtn.setAttribute('aria-label', '复制 Token 到剪贴板');
            copyBtn.addEventListener('click', async () => {
                try {
                    await navigator.clipboard.writeText(token);
                    showNotification('Token 已复制到剪贴板');
                } catch (err) {
                    showNotification('复制失败，请重试');
                }
            });

            const deleteBtn = document.createElement('button');
            deleteBtn.className = 'btn btn-danger btn-base';
            deleteBtn.innerHTML = '<svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M3 6h18M9 6V4a1 1 0 0 1 1-1h4a1 1 0 0 1 1 1v2M10 11v6M14 11v6M4 6v14a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V6"/></svg>';
            deleteBtn.setAttribute('aria-label', `删除 Token ${token}`);
            deleteBtn.addEventListener('click', async () => {
                if (confirm(`确认删除 token: ${token}?`)) {
                    try {
                        const baseUrl = document.getElementById('baseUrl').value;
                        const response = await fetch(`${baseUrl}/manager/api/delete`, {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ sso: token })
                        });
                        if (response.ok) {
                            await fetchTokenMap();
                            showNotification('Token 删除成功');
                        } else {
                            showNotification('删除 Token 失败');
                        }
                    } catch (error) {
                        showNotification('删除 Token 出错');
                    }
                }
            });

            tokenActions.appendChild(copyBtn);
            tokenActions.appendChild(deleteBtn);
            tokenHeader.appendChild(tokenTitle);
            tokenHeader.appendChild(tokenActions);

            const modelList = document.createElement('div');
            modelList.className = 'model-list';

            Object.entries(modelConfig).forEach(([modelName, config]) => {
                const modelItem = document.createElement('div');
                modelItem.className = 'model-item';

                const modelNameSpan = document.createElement('div');
                modelNameSpan.className = 'model-name';
                modelNameSpan.textContent = modelName;

                const progressContainer = document.createElement('div');
                progressContainer.className = 'progress-container';

                const progressBar = document.createElement('div');
                progressBar.className = 'progress-bar';
                const progressFill = document.createElement('div');
                progressFill.className = 'progress-fill';

                const modelData = tokenData[modelName];
                const requestCount = modelData.totalRequestCount;
                const maxRequests = config.RequestFrequency;
                const percentage = Math.min((requestCount / maxRequests) * 100, 100);
                progressFill.style.width = `${percentage}%`;
                progressFill.style.backgroundColor = getProgressColor(percentage, modelData.isValid);
                progressBar.appendChild(progressFill);

                const progressText = document.createElement('div');
                progressText.className = 'progress-text';
                progressText.textContent = `${requestCount}/${maxRequests}`;

                const status = document.createElement('div');
                status.className = 'status';
                if (!modelData.isValid) {
                    status.classList.add('status-expired');
                    status.textContent = '失效';
                    status.setAttribute('data-invalidated-time', modelData.invalidatedTime);
                    status.setAttribute('data-expiration-time', config.ExpirationTime);
                    const tooltip = document.createElement('div');
                    tooltip.className = 'tooltip';
                    tooltip.textContent = getTooltipText(modelData.invalidatedTime, config.ExpirationTime);
                    status.appendChild(tooltip);
                } else {
                    status.classList.add('status-active');
                    status.textContent = '活跃';
                }

                progressContainer.appendChild(progressBar);
                progressContainer.appendChild(progressText);

                modelItem.appendChild(modelNameSpan);
                modelItem.appendChild(progressContainer);
                modelItem.appendChild(status);
                modelList.appendChild(modelItem);
            });

            tokenCard.appendChild(checkbox);
            tokenCard.appendChild(tokenHeader);
            tokenCard.appendChild(modelList);
            return tokenCard;
        }

        function updateTokenCard(token, tokenData) {
            const tokenCard = document.querySelector(`[data-token="${token}"]`);
            const modelItems = tokenCard.querySelectorAll('.model-item');
            let index = 0;
            Object.entries(modelConfig).forEach(([modelName, config]) => {
                const modelItem = modelItems[index++];
                const modelData = tokenData[modelName];
                const requestCount = modelData.totalRequestCount;
                const maxRequests = config.RequestFrequency;
                const percentage = Math.min((requestCount / maxRequests) * 100, 100);

                const progressFill = modelItem.querySelector('.progress-fill');
                progressFill.style.width = `${percentage}%`;
                progressFill.style.backgroundColor = getProgressColor(percentage, modelData.isValid);

                const progressText = modelItem.querySelector('.progress-text');
                progressText.textContent = `${requestCount}/${maxRequests}`;

                const status = modelItem.querySelector('.status');
                status.className = 'status';
                if (!modelData.isValid) {
                    status.classList.add('status-expired');
                    status.textContent = '失效';
                    status.setAttribute('data-invalidated-time', modelData.invalidatedTime);
                    status.setAttribute('data-expiration-time', config.ExpirationTime);
                    const tooltip = status.querySelector('.tooltip') || document.createElement('div');
                    tooltip.className = 'tooltip';
                    tooltip.textContent = getTooltipText(modelData.invalidatedTime, config.ExpirationTime);
                    if (!status.contains(tooltip)) status.appendChild(tooltip);
                } else {
                    status.classList.add('status-active');
                    status.textContent = '活跃';
                    const tooltip = status.querySelector('.tooltip');
                    if (tooltip) tooltip.remove();
                }
            });
            const checkbox = tokenCard.querySelector('.token-checkbox');
            checkbox.className = `token-checkbox ${batchDeleteMode ? 'show' : ''}`;
        }

        function renderTokenDiff(newTokenMap) {
            const tokenGrid = document.getElementById('tokenGrid');
            if (!tokenGrid) {
                console.error('Token grid element not found.');
                return;
            }
            const existingTokens = new Set(Array.from(tokenGrid.children).map(card => card.getAttribute('data-token')));
            const filteredTokens = filterTokensArray(Object.entries(newTokenMap));
            const totalItems = filteredTokens.length;
            const totalPages = Math.ceil(totalItems / itemsPerPage);
            currentPage = Math.min(currentPage, totalPages) || 1;

            const startIndex = (currentPage - 1) * itemsPerPage;
            const endIndex = Math.min(startIndex + itemsPerPage, totalItems);
            const tokensToRender = filteredTokens.slice(startIndex, endIndex);

            const newTokens = new Set(tokensToRender.map(([token]) => token));

            existingTokens.forEach(token => {
                if (!newTokens.has(token)) {
                    tokenGrid.querySelector(`[data-token="${token}"]`).remove();
                }
            });

            tokensToRender.forEach(([token, tokenData]) => {
                if (!existingTokens.has(token)) {
                    const tokenCard = createTokenCard(token, tokenData);
                    tokenGrid.appendChild(tokenCard);
                } else {
                    updateTokenCard(token, tokenData);
                }
            });

            updateTokenCounters();
            renderPagination(totalPages);
        }

        function filterTokensArray(tokenEntries) {
            const searchInput = document.getElementById('searchInput');
            const statusFilter = document.getElementById('statusFilter');
            if (!searchInput || !statusFilter) {
                console.error('Search input or status filter element not found.');
                return tokenEntries;
            }
            const searchTerm = searchInput.value.toLowerCase();
            const statusFilterValue = statusFilter.value;

            return tokenEntries.filter(([token, tokenData]) => {
                const hasActive = Object.values(tokenData).some(data => data.isValid);
                const hasExpired = Object.values(tokenData).some(data => !data.isValid);

                let display = true;
                if (!token.toLowerCase().includes(searchTerm)) display = false;
                if (statusFilterValue === 'active' && !hasActive) display = false;
                if (statusFilterValue === 'expired' && !hasExpired) display = false;

                return display;
            });
        }

        function renderPagination(totalPages) {
            const pageSelect = document.getElementById('pageSelect');
            if (!pageSelect) {
                console.error('Page select element not found.');
                return;
            }
            pageSelect.innerHTML = '';
            for (let i = 1; i <= totalPages; i++) {
                const option = document.createElement('option');
                option.value = i;
                option.textContent = `第 ${i} 页`;
                if (i === currentPage) option.selected = true;
                pageSelect.appendChild(option);
            }

            const prevPage = document.getElementById('prevPage');
            const nextPage = document.getElementById('nextPage');
            if (prevPage) prevPage.disabled = currentPage === 1;
            if (nextPage) nextPage.disabled = currentPage === totalPages || totalPages === 0;
        }

        async function fetchTokenMap() {
            console.log('开始获取 tokenMap');
            try {
                const baseUrlElement = document.getElementById('baseUrl');
                if (!baseUrlElement) {
                    throw new Error('Base URL 元素未找到');
                }
                const baseUrl = baseUrlElement.value;
                console.log('请求 URL:', `${baseUrl}/manager/api/get`);
                const response = await fetch(`${baseUrl}/manager/api/get`);
                if (!response.ok) {
                    const errorText = await response.text();
                    throw new Error(`获取 Token 失败: ${response.status} - ${errorText}`);
                }
                const data = await response.json();
                console.log('获取到的数据:', data);
                if (!data || typeof data !== 'object') {
                    throw new Error('返回的数据不是有效的 Token Map');
                }
                tokenMap = data;
                renderTokenDiff(tokenMap);
                console.log('tokenMap 更新成功');
            } catch (error) {
                console.error('获取 Token 出错:', error);
                showNotification(`获取 Token 出错: ${error.message}`);
            }
        }

        document.addEventListener('DOMContentLoaded', () => {
            const baseUrlInput = document.getElementById('baseUrl');
            if (baseUrlInput) {
                baseUrlInput.value = window.location.origin;
            } else {
                console.error('Base URL input element not found.');
            }

            const apiKeyInput = document.getElementById('apiKey');
            if (apiKeyInput) {
                const savedApiKey = localStorage.getItem('apiKey');
                apiKeyInput.value = savedApiKey || 'sk-1234567';
                apiKeyInput.addEventListener('change', () => {
                    const newApiKey = apiKeyInput.value.trim();
                    if (newApiKey) {
                        localStorage.setItem('apiKey', newApiKey);
                        showNotification(`API Key 已更新为: ${newApiKey}`);
                    } else {
                        apiKeyInput.value = localStorage.getItem('apiKey') || 'sk-1234567';
                        showNotification('API Key 不能为空，已恢复为上次保存的值');
                    }
                });
            } else {
                console.error('API Key input element not found.');
            }

            const copyBaseUrlBtn = document.getElementById('copyBaseUrlBtn');
            if (copyBaseUrlBtn) {
                copyBaseUrlBtn.addEventListener('click', async () => {
                    try {
                        await navigator.clipboard.writeText(baseUrlInput.value);
                        showNotification('Base URL 已复制到剪贴板');
                    } catch (err) {
                        showNotification('复制失败，请重试');
                    }
                });
            }

            const copyApiKeyBtn = document.getElementById('copyApiKeyBtn');
            if (copyApiKeyBtn) {
                copyApiKeyBtn.addEventListener('click', async () => {
                    try {
                        await navigator.clipboard.writeText(apiKeyInput.value);
                        showNotification('API Key 已复制到剪贴板');
                    } catch (err) {
                        showNotification('复制失败，请重试');
                    }
                });
            }

            const addSingleTokenBtn = document.getElementById('addSingleTokenBtn');
            if (addSingleTokenBtn) {
                addSingleTokenBtn.addEventListener('click', async () => {
                    const tokenInput = document.getElementById('singleTokenInput');
                    const tokenText = tokenInput.value.trim();
                    if (tokenText) {
                        try {
                            const baseUrl = document.getElementById('baseUrl').value;
                            const response = await fetch(`${baseUrl}/manager/api/add`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ sso: tokenText })
                            });
                            if (response.ok) {
                                tokenInput.value = '';
                                await fetchTokenMap();
                                showNotification('Token 添加成功');
                            } else {
                                showNotification('添加 Token 失败');
                            }
                        } catch (error) {
                            showNotification('添加 Token 出错');
                        }
                    } else {
                        showNotification('请输入 Token');
                    }
                });
            }

            const addBatchTokenBtn = document.getElementById('addBatchTokenBtn');
            if (addBatchTokenBtn) {
                addBatchTokenBtn.addEventListener('click', async () => {
                    const tokenInput = document.getElementById('batchTokenInput');
                    const tokenText = tokenInput.value.trim();
                    if (tokenText) {
                        const tokens = tokenText.split(',').map(t => t.trim()).filter(t => t.length > 0);
                        if (tokens.length === 0) {
                            showNotification('请输入至少一个有效的 Token');
                            return;
                        }
                        try {
                            const baseUrl = document.getElementById('baseUrl').value;
                            const successes = [];
                            const failures = [];
                            for (const token of tokens) {
                                const response = await fetch(`${baseUrl}/manager/api/add`, {
                                    method: 'POST',
                                    headers: { 'Content-Type': 'application/json' },
                                    body: JSON.stringify({ sso: token })
                                });
                                if (response.ok) {
                                    successes.push(token);
                                } else {
                                    failures.push(token);
                                }
                            }
                            tokenInput.value = '';
                            await fetchTokenMap();
                            if (successes.length > 0 && failures.length === 0) {
                                showNotification(`成功添加 ${successes.length} 个 Token`);
                            } else if (successes.length > 0 && failures.length > 0) {
                                showNotification(`成功添加 ${successes.length} 个 Token，失败 ${failures.length} 个`);
                            } else {
                                showNotification('所有 Token 添加失败');
                            }
                        } catch (error) {
                            showNotification('添加 Token 时出错');
                        }
                    } else {
                        showNotification('请输入 Token');
                    }
                });
            }

            const setCfBtn = document.getElementById('setCfBtn');
            if (setCfBtn) {
                setCfBtn.addEventListener('click', async () => {
                    const cfInput = document.getElementById('cfInput');
                    const newCf = cfInput.value.trim();
                    if (newCf) {
                        try {
                            const baseUrl = document.getElementById('baseUrl').value;
                            const response = await fetch(`${baseUrl}/manager/api/cf_clearance`, {
                                method: 'POST',
                                headers: { 'Content-Type': 'application/json' },
                                body: JSON.stringify({ cf_clearance: newCf })
                            });
                            if (response.ok) {
                                cfInput.value = '';
                                showNotification('CF Clearance 设置成功');
                            } else {
                                showNotification('设置 CF Clearance 失败');
                            }
                        } catch (error) {
                            showNotification('设置 CF Clearance 出错');
                        }
                    }
                });
            }

            const testAvailabilityBtn = document.getElementById('testAvailabilityBtn');
            if (testAvailabilityBtn) {
                testAvailabilityBtn.addEventListener('click', async () => {
                    const selectedModel = document.getElementById('modelSelect').value;
                    const baseUrl = document.getElementById('baseUrl').value;
                    const apiKey = document.getElementById('apiKey').value.trim();
                    const apiUrl = `${baseUrl}/v1/chat/completions`;
                    try {
                        const response = await fetch(apiUrl, {
                            method: 'POST',
                            headers: {
                                'Content-Type': 'application/json',
                                'Authorization': `Bearer ${apiKey}`
                            },
                            body: JSON.stringify({
                                model: selectedModel,
                                messages: [{ role: 'user', content: 'Hello' }],
                                max_tokens: 10
                            })
                        });
                        if (response.ok) {
                            const data = await response.json();
                            if (data.choices && data.choices.length > 0) {
                                showNotification(`${selectedModel} 模型可用`);
                            } else {
                                showNotification(`${selectedModel} 模型不可用`);
                            }
                        } else {
                            const errorText = await response.text();
                            showNotification(`${selectedModel} 模型不可用（错误: ${response.status} - ${errorText}）`);
                        }
                    } catch (error) {
                        console.error('检测模型出错:', error);
                        showNotification(`检测 ${selectedModel} 失败，请检查网络或接口`);
                    }
                });
            }

            const batchDeleteTokens = document.getElementById('batchDeleteTokens');
            if (batchDeleteTokens) {
                batchDeleteTokens.addEventListener('click', async () => {
                    if (!batchDeleteMode) {
                        batchDeleteMode = true;
                        renderTokenDiff(tokenMap);
                        showNotification('请选择要删除的 Token');
                    } else {
                        const selectedTokens = Array.from(document.querySelectorAll('.token-checkbox:checked')).map(cb => cb.value);
                        if (selectedTokens.length === 0) {
                            showNotification('请至少选择一个 Token');
                            return;
                        }
                        if (confirm(`确认删除 ${selectedTokens.length} 个 Token?`)) {
                            try {
                                const baseUrl = document.getElementById('baseUrl').value;
                                const successes = [];
                                const failures = [];
                                for (const token of selectedTokens) {
                                    const response = await fetch(`${baseUrl}/manager/api/delete`, {
                                        method: 'POST',
                                        headers: { 'Content-Type': 'application/json' },
                                        body: JSON.stringify({ sso: token })
                                    });
                                    if (response.ok) {
                                        successes.push(token);
                                    } else {
                                        failures.push(token);
                                    }
                                }
                                batchDeleteMode = false;
                                await fetchTokenMap();
                                if (successes.length > 0 && failures.length === 0) {
                                    showNotification(`成功删除 ${successes.length} 个 Token`);
                                } else if (successes.length > 0 && failures.length > 0) {
                                    showNotification(`成功删除 ${successes.length} 个 Token，失败 ${failures.length} 个`);
                                } else {
                                    showNotification('所有 Token 删除失败');
                                }
                            } catch (error) {
                                showNotification('删除 Token 时出错');
                            }
                        }
                    }
                });
            }

            const searchInput = document.getElementById('searchInput');
            if (searchInput) searchInput.addEventListener('input', () => renderTokenDiff(tokenMap));

            const statusFilter = document.getElementById('statusFilter');
            if (statusFilter) statusFilter.addEventListener('change', () => renderTokenDiff(tokenMap));

            const refreshTokens = document.getElementById('refreshTokens');
            if (refreshTokens) {
                refreshTokens.addEventListener('click', async () => {
                    await fetchTokenMap();
                    showNotification('Token 列表已刷新');
                });
            } else {
                console.error('Refresh Tokens button not found.');
            }

            const prevPage = document.getElementById('prevPage');
            if (prevPage) {
                prevPage.addEventListener('click', () => {
                    if (currentPage > 1) {
                        currentPage--;
                        renderTokenDiff(tokenMap);
                    }
                });
            }

            const nextPage = document.getElementById('nextPage');
            if (nextPage) {
                nextPage.addEventListener('click', () => {
                    const totalPages = Math.ceil(filterTokensArray(Object.entries(tokenMap)).length / itemsPerPage);
                    if (currentPage < totalPages) {
                        currentPage++;
                        renderTokenDiff(tokenMap);
                    }
                });
            }

            const pageSelect = document.getElementById('pageSelect');
            if (pageSelect) {
                pageSelect.addEventListener('change', (e) => {
                    currentPage = parseInt(e.target.value, 10);
                    renderTokenDiff(tokenMap);
                });
            }

            fetchTokenMap(); // 页面加载时获取 Token

            let timer = setInterval(updateExpiredTokenTimers, 60000);
            document.addEventListener('visibilitychange', () => {
                if (document.hidden) {
                    clearInterval(timer);
                } else {
                    timer = setInterval(updateExpiredTokenTimers, 60000);
                }
            });
        });

        function showNotification(message) {
            const notification = document.getElementById('notification');
            if (notification) {
                notification.textContent = message;
                notification.style.display = 'block';
                const duration = Math.max(2000, message.length * 100);
                setTimeout(() => {
                    notification.style.display = 'none';
                }, duration);
            } else {
                console.warn('Notification element not found.');
            }
        }
    </script>
</body>
</html>
